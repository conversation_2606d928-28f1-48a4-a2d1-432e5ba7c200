# Правила проекта EasyLinkLife

**Домен**: easylinklife.com  
**IP сервера**: *************  
**Окружение**: Production  

## 🎯 Общие принципы разработки

### Архитектурные принципы
- **Микросервисная архитектура** - каждый сервис отвечает за свою область
- **API-first подход** - все взаимодействие через REST API
- **Database per service** - каждый сервис имеет свою базу данных
- **Stateless сервисы** - состояние хранится в Redis/PostgreSQL
- **Circuit breaker pattern** - защита от каскадных отказов

### Технологический стек
- **Frontend**: Next.js 14 с TypeScript, Tailwind CSS
- **Backend**: Node.js с TypeScript, Express/Fastify
- **Database**: PostgreSQL 16 с партиционированием
- **Cache**: Redis 7 с Sentinel
- **Search**: Meilisearch с OpenAI интеграцией
- **API Gateway**: Kong с JWT аутентификацией
- **Monitoring**: Prometheus + Grafana + Loki

## 🔒 Безопасность

### Обязательные требования
- Все SQL запросы только через параметризованные запросы
- JWT токены с коротким временем жизни (15 минут)
- Rate limiting на всех API endpoints
- Input validation на всех входящих данных
- HTTPS везде, HTTP редиректы на HTTPS
- Secrets только через environment variables

### Fraud Detection
- Каждый клик проходит через fraud detection
- Блокировка при score >= 80 баллов
- Логирование всех подозрительных активностей
- IP reputation checking с кешированием

## 📊 Производительность

### Целевые метрики
- Response time < 300ms для кешированного контента
- Search response time < 500ms
- Database query time < 50ms
- 99.9% uptime
- Throughput > 1000 RPS

### Кеширование
- Redis для session данных и частых запросов
- CDN (Cloudflare) для статических ресурсов
- Database query caching через PgBouncer
- Meilisearch для поисковых запросов

## 🌍 Мультирегиональность

### Поддерживаемые страны СНГ
- Россия (RU) - русский, RUB
- Казахстан (KZ) - казахский, KZT  
- Беларусь (BY) - белорусский, BYN
- Узбекистан (UZ) - узбекский, UZS
- Кыргызстан (KG) - киргизский, KGS
- Таджикистан (TJ) - таджикский, TJS
- Туркменистан (TM) - туркменский, TMT
- Армения (AM) - армянский, AMD
- Азербайджан (AZ) - азербайджанский, AZN
- Грузия (GE) - грузинский, GEL
- Молдова (MD) - румынский, MDL

### Локализация
- Автоопределение страны по IP
- Конвертация валют в реальном времени
- Региональная фильтрация офферов
- Локализованный контент

## 🔍 SEO и контент

### Автогенерация контента
- SEO заголовки и мета-описания для каждого оффера
- FAQ секции с Schema.org разметкой
- Списки плюсов/минусов
- Связанные офферы и рекомендации
- Уникальный контент для избежания дублирования

### Структура URL
- Русскоязычные URL для СНГ рынка
- Семантически правильная структура
- Canonical URLs для избежания дублирования
- Breadcrumb навигация

## 📝 Стандарты кода

### TypeScript
- Строгий режим TypeScript
- Обязательная типизация всех функций
- Интерфейсы для всех API responses
- Enum для констант

### Именование
- camelCase для переменных и функций
- PascalCase для классы и интерфейсы
- UPPER_SNAKE_CASE для констант
- kebab-case для файлов и папок

### Структура проекта
```
apps/
  web/                 # Next.js frontend
  admin/              # Admin panel
services/
  offer/              # Offer management service
  search/             # Search service
  email/              # Email service
  geo/                # Geo service
  fraud-detection/    # Fraud detection service
workers/
  offer-importer/     # Offer import worker
  currency-updater/   # Currency update worker
  seo-generator/      # SEO content generator
```

## 🧪 Тестирование

### Обязательные тесты
- Unit tests для всех сервисов (coverage > 80%)
- Integration tests для API endpoints
- E2E tests для критических user journeys
- Load tests для производительности
- Security tests (OWASP ZAP)

### Инструменты
- Jest для unit/integration тестов
- Playwright для E2E тестов
- K6 для load тестирования
- Snyk для security сканирования

## 🚀 Deployment

### CI/CD Pipeline
- Автоматические тесты на каждый PR
- Security сканирование перед деплоем
- Staging deployment для тестирования
- Blue-green deployment в production
- Rollback план для каждого релиза

### Мониторинг
- Health checks для всех сервисов
- Prometheus метрики
- Grafana дашборды
- Алерты для критических метрик
- Centralized logging через Loki

## 📋 Definition of Done

### Для каждой задачи
- [ ] Код написан и протестирован
- [ ] Unit tests покрывают новый функционал
- [ ] Integration tests проходят
- [ ] Security review пройден
- [ ] Performance тестирование выполнено
- [ ] Documentation обновлена
- [ ] Code review завершен
- [ ] Staging deployment успешен

### Для релиза
- [ ] Все тесты проходят
- [ ] Security сканирование чистое
- [ ] Load testing показывает приемлемую производительность
- [ ] Monitoring и алерты настроены
- [ ] Rollback план готов
- [ ] Documentation актуальна