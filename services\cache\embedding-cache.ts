export class EmbeddingCacheService {
  private redis: Redis;
  private openai: OpenAI;
  
  constructor() {
    this.redis = new Redis(process.env.REDIS_URL);
    this.openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
  }
  
  async getOrCreateEmbedding(text: string): Promise<number[]> {
    const cacheKey = `embedding:${this.hashText(text)}`;
    
    // Проверяем кеш
    const cached = await this.redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }
    
    // Создаем новый embedding
    const response = await this.openai.embeddings.create({
      model: "text-embedding-3-small",
      input: text,
      encoding_format: "float"
    });
    
    const embedding = response.data[0].embedding;
    
    // Кешируем на 24 часа
    await this.redis.setex(cacheKey, 86400, JSON.stringify(embedding));
    
    return embedding;
  }
  
  async batchCreateEmbeddings(texts: string[]): Promise<number[][]> {
    const cacheKeys = texts.map(text => `embedding:${this.hashText(text)}`);
    const cached = await this.redis.mget(...cacheKeys);
    
    const uncachedTexts: string[] = [];
    const uncachedIndices: number[] = [];
    
    cached.forEach((item, index) => {
      if (!item) {
        uncachedTexts.push(texts[index]);
        uncachedIndices.push(index);
      }
    });
    
    // Создаем embeddings только для некешированных текстов
    let newEmbeddings: number[][] = [];
    if (uncachedTexts.length > 0) {
      const response = await this.openai.embeddings.create({
        model: "text-embedding-3-small",
        input: uncachedTexts,
        encoding_format: "float"
      });
      
      newEmbeddings = response.data.map(item => item.embedding);
      
      // Кешируем новые embeddings
      const pipeline = this.redis.pipeline();
      uncachedTexts.forEach((text, index) => {
        const cacheKey = `embedding:${this.hashText(text)}`;
        pipeline.setex(cacheKey, 86400, JSON.stringify(newEmbeddings[index]));
      });
      await pipeline.exec();
    }
    
    // Собираем результат
    const result: number[][] = [];
    let newEmbeddingIndex = 0;
    
    cached.forEach((item, index) => {
      if (item) {
        result[index] = JSON.parse(item);
      } else {
        result[index] = newEmbeddings[newEmbeddingIndex++];
      }
    });
    
    return result;
  }
  
  private hashText(text: string): string {
    return crypto.createHash('sha256').update(text).digest('hex');
  }
}