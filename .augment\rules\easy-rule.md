---
type: "agent_requested"
description: "Example description"
---
</augment_code_snippet>

---

Теперь создам новые Rules для Augment:

````markdown path=Rules-EasyLinkLife-RAG-Development.md mode=EDIT
# Rules для разработки EasyLinkLife RAG-системы

## 🎯 Основные принципы разработки

### Архитектурные правила
- **RAG-first подход** - все решения должны учитывать потребности AI чатов
- **Структурированные данные** - каждый элемент должен иметь Schema.org разметку
- **Семантическая плотность** - минимум 35 сущностей на 1000 слов контента
- **Кеширование embeddings** - обязательное кеширование всех векторных представлений
- **Real-time актуальность** - данные должны обновляться в реальном времени

### Технологические требования
- **TypeScript строгий режим** - все типы должны быть явно определены
- **OpenAI API оптимизация** - минимизация запросов через кеширование
- **Meilisearch + векторный поиск** - гибридный подход к поиску
- **Redis для кеширования** - многоуровневое кеширование данных
- **PostgreSQL для метаданных** - структурированное хранение связей

## 🤖 AI-специфичные требования

### Обязательные элементы для AI чатов
- Каждый оффер ДОЛЖЕН иметь структурированное описание с сущностями
- FAQ секции ДОЛЖНЫ быть в формате Schema.org FAQPage
- Все цены ДОЛЖНЫ включать валюту и регион доступности
- Рейтинги ДОЛЖНЫ быть подтверждены реальными отзывами
- Связанные продукты ДОЛЖНЫ быть явно указаны через граф связей

### Форматы ответов для AI
- **Structured JSON** для программного парсинга
- **Markdown с метаданными** для читаемости
- **Schema.org LD+JSON** для семантического понимания
- **Confidence scores** для оценки достоверности
- **Source attribution** для проверки источников

## 🔍 Качество контента

### Семантические требования
- Каждая сущность ДОЛЖНА иметь четкое определение
- Отношения между сущностями ДОЛЖНЫ быть явно указаны
- Синонимы ДОЛЖНЫ быть определены для всех языков СНГ
- Контекстуальные связи ДОЛЖНЫ быть машиночитаемыми

### Актуальность данных
- Статус офферов ДОЛЖЕН проверяться каждые 5 минут
- Цены ДОЛЖНЫ обновляться в реальном времени
- Рейтинги ДОЛЖНЫ пересчитываться ежедневно
- Недоступные офферы ДОЛЖНЫ помечаться немедленно

## 📊 Мониторинг и метрики

### Обязательные метрики
- **AI request rate** - количество запросов от AI чатов
- **RAG accuracy** - точность семантического поиска
- **Cache hit rate** - эффективность кеширования
- **Response time** - время ответа для AI запросов
- **Data freshness** - актуальность данных

### Алерты
- Падение точности RAG ниже 90%
- Время ответа AI API больше 500ms
- Cache hit rate ниже 80%
- Недоступность OpenAI API
- Устаревание данных более 1 часа

## 🚀 Deployment и CI/CD

### Обязательные проверки перед деплоем
- [ ] Все embeddings кешируются корректно
- [ ] AI API возвращает структурированные ответы
- [ ] Schema.org разметка валидна
- [ ] Real-time обновления работают
- [ ] Мониторинг AI метрик настроен

### Тестирование
- **Unit tests** для всех RAG компонентов
- **Integration tests** для AI API endpoints
- **E2E tests** с реальными AI чатами
- **Performance tests** для embedding операций
- **Schema validation tests** для всех страниц

## 🔒 Безопасность и ограничения

### Rate limiting для AI
- Максимум 100 запросов в минуту с одного IP
- Специальные лимиты для известных AI ботов
- Graceful degradation при превышении лимитов

### Защита данных
- Логирование всех AI запросов для анализа
- Анонимизация пользовательских данных
- Защита от scraping через bot detection

## 📝 Документация

### Обязательная документация
- API документация для AI интеграций
- Schema.org mapping для всех сущностей
- Примеры запросов и ответов для AI
- Troubleshooting guide для AI проблем

### Комментарии в коде
- Все RAG функции ДОЛЖНЫ быть документированы
- Embedding логика ДОЛЖНА иметь примеры
- AI-specific код ДОЛЖЕН содержать контекст
- Performance критичные участки ДОЛЖНЫ быть помечены