export class RealTimeOfferUpdater {
  private meilisearch: <PERSON><PERSON>Search;
  private redis: Redis;
  
  constructor() {
    this.meilisearch = new MeiliSearch({ host: process.env.MEILISEARCH_URL });
    this.redis = new Redis(process.env.REDIS_URL);
  }
  
  @Cron('*/5 * * * *') // Каждые 5 минут
  async updateOfferAvailability() {
    const offers = await this.getActiveOffers();
    
    const updatePromises = offers.map(async (offer) => {
      try {
        const isAvailable = await this.checkOfferAvailability(offer.tracking_url);
        const currentStatus = offer.status;
        
        if (isAvailable !== (currentStatus === 'active')) {
          const newStatus = isAvailable ? 'active' : 'inactive';
          
          // Обновляем в базе данных
          await this.updateOfferStatus(offer.id, newStatus);
          
          // Обновляем в Meilisearch
          await this.meilisearch.index('offers').updateDocuments([{
            id: offer.id,
            status: newStatus,
            updated_at: new Date().toISOString(),
            availability_checked_at: new Date().toISOString()
          }]);
          
          // Инвалидируем кеш
          await this.redis.del(`offer:${offer.id}`);
          
          // Логируем изменение
          console.log(`Offer ${offer.id} status changed: ${currentStatus} -> ${newStatus}`);
        }
      } catch (error) {
        console.error(`Failed to update offer ${offer.id}:`, error);
      }
    });
    
    await Promise.allSettled(updatePromises);
  }
  
  private async checkOfferAvailability(trackingUrl: string): Promise<boolean> {
    try {
      const response = await fetch(trackingUrl, {
        method: 'HEAD',
        timeout: 5000,
        redirect: 'follow'
      });
      
      // Считаем оффер доступным если получили успешный ответ
      return response.ok;
    } catch (error) {
      return false;
    }
  }
  
  @EventPattern('offer.updated')
  async handleOfferUpdate(data: { offerId: string; changes: Partial<Offer> }) {
    const { offerId, changes } = data;
    
    // Обновляем в Meilisearch
    await this.meilisearch.index('offers').updateDocuments([{
      id: offerId,
      ...changes,
      updated_at: new Date().toISOString()
    }]);
    
    // Инвалидируем связанные кеши
    await this.invalidateRelatedCaches(offerId);
    
    // Уведомляем AI системы об обновлении
    await this.notifyAISystems(offerId, changes);
  }
  
  private async notifyAISystems(offerId: string, changes: Partial<Offer>) {
    // Webhook для уведомления внешних AI систем
    const webhookUrl = process.env.AI_WEBHOOK_URL;
    if (webhookUrl) {
      try {
        await fetch(webhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            event: 'offer.updated',
            offer_id: offerId,
            changes,
            timestamp: new Date().toISOString(),
            source: 'easylinklife.com'
          })
        });
      } catch (error) {
        console.error('Failed to notify AI systems:', error);
      }
    }
  }
}