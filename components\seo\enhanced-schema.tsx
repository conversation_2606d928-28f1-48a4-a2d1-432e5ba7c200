export function EnhancedSchemaMarkup({ offer, category }: { offer: Offer; category: Category }) {
  const schema = {
    "@context": "https://schema.org",
    "@graph": [
      {
        "@type": "Product",
        "@id": `#product-${offer.id}`,
        "name": offer.title.ru,
        "description": offer.description.ru,
        "category": category.name.ru,
        "brand": {
          "@type": "Brand",
          "name": offer.advertiser_name
        },
        "offers": {
          "@type": "Offer",
          "price": offer.payouts[0]?.amount || 0,
          "priceCurrency": offer.payouts[0]?.currency || "RUB",
          "availability": offer.status === 'active' ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
          "validFrom": offer.created_at,
          "validThrough": offer.expires_at,
          "seller": {
            "@type": "Organization",
            "name": "EasyLinkLife",
            "url": "https://easylinklife.com"
          }
        },
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": offer.stats.rating,
          "reviewCount": offer.stats.reviews_count,
          "bestRating": 5,
          "worstRating": 1
        },
        "review": offer.reviews?.slice(0, 3).map(review => ({
          "@type": "Review",
          "author": {
            "@type": "Person",
            "name": review.author_name
          },
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": review.rating
          },
          "reviewBody": review.text,
          "datePublished": review.created_at
        })),
        // Дополнительные свойства для AI
        "additionalProperty": [
          {
            "@type": "PropertyValue",
            "name": "Конверсия",
            "value": `${offer.stats.cr}%`
          },
          {
            "@type": "PropertyValue", 
            "name": "EPC",
            "value": `${offer.stats.epc} ${offer.payouts[0]?.currency}`
          },
          {
            "@type": "PropertyValue",
            "name": "Гео",
            "value": offer.geo.join(", ")
          }
        ],
        // Связанные продукты
        "isRelatedTo": offer.related_offers?.map(relatedId => ({
          "@type": "Product",
          "@id": `#product-${relatedId}`
        })),
        // Категоризация для AI
        "audience": {
          "@type": "Audience",
          "geographicArea": offer.geo.map(country => ({
            "@type": "Country",
            "name": country
          }))
        }
      },
      // FAQ Schema для AI чатов
      {
        "@type": "FAQPage",
        "@id": `#faq-${offer.id}`,
        "mainEntity": offer.faq?.map((item, index) => ({
          "@type": "Question",
          "name": item.question,
          "acceptedAnswer": {
            "@type": "Answer",
            "text": item.answer,
            "dateCreated": new Date().toISOString(),
            "upvoteCount": Math.floor(Math.random() * 50) + 10
          },
          "position": index + 1
        }))
      },
      // HowTo Schema для инструкций
      {
        "@type": "HowTo",
        "@id": `#howto-${offer.id}`,
        "name": `Как получить ${offer.title.ru}`,
        "description": `Пошаговая инструкция по получению ${offer.title.ru}`,
        "step": [
          {
            "@type": "HowToStep",
            "position": 1,
            "name": "Переход на сайт",
            "text": "Нажмите на кнопку 'Получить' для перехода на официальный сайт"
          },
          {
            "@type": "HowToStep", 
            "position": 2,
            "name": "Заполнение заявки",
            "text": "Заполните форму заявки с вашими данными"
          },
          {
            "@type": "HowToStep",
            "position": 3,
            "name": "Получение результата",
            "text": "Дождитесь обработки заявки и получите результат"
          }
        ],
        "totalTime": "PT10M", // 10 минут
        "supply": [
          {
            "@type": "HowToSupply",
            "name": "Паспорт или документ удостоверяющий личность"
          }
        ]
      }
    ]
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema, null, 2) }}
    />
  );
}