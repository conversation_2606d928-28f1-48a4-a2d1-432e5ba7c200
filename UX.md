<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EasyLinkLife.com - Поиск лучших аффилиатных офферов СНГ</title>
    <meta name="description" content="AI-поиск среди тысяч проверенных офферов. Сравнивайте, выбирайте, экономьте на EasyLinkLife.com">
    
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#2563EB',
                        'accent': '#10B981',
                        'muted': '#6B7280'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'bounce-slow': 'bounce 2s infinite',
                    }
                }
            }
        }
    </script>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { 
                opacity: 0;
                transform: translateY(20px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .search-input {
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            transform: scale(1.02);
        }
        
        .flag-icon {
            width: 24px;
            height: 16px;
            border-radius: 2px;
            background-size: cover;
        }
    </style>
</head>
<body class="font-sans bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-primary">EasyLinkLife</h1>
                    </div>
                </div>
                
                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex items-center space-x-8">
                    <a href="#finance" class="text-gray-700 hover:text-primary transition-colors">Финансы</a>
                    <a href="#tech" class="text-gray-700 hover:text-primary transition-colors">Технологии</a>
                    <a href="#health" class="text-gray-700 hover:text-primary transition-colors">Здоровье</a>
                    <a href="#education" class="text-gray-700 hover:text-primary transition-colors">Образование</a>
                    <a href="#travel" class="text-gray-700 hover:text-primary transition-colors">Туризм</a>
                    <a href="#business" class="text-gray-700 hover:text-primary transition-colors">Бизнес</a>
                    <a href="#blog" class="text-gray-700 hover:text-primary transition-colors">Блог</a>
                </nav>
                
                <!-- Search Bar -->
                <div class="hidden md:flex items-center flex-1 max-w-md mx-8">
                    <div class="relative w-full">
                        <input type="text" 
                               placeholder="Найти лучшие предложения..." 
                               class="search-input w-full px-4 py-2 pl-10 pr-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" width="20"></i>
                    </div>
                </div>
                
                <!-- Language/Currency Switcher -->
                <div class="hidden lg:flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <select class="text-sm border border-gray-300 rounded px-2 py-1">
                            <option>RU</option>
                            <option>KZ</option>
                            <option>BY</option>
                            <option>UZ</option>
                        </select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <select class="text-sm border border-gray-300 rounded px-2 py-1">
                            <option>RUB</option>
                            <option>KZT</option>
                            <option>BYN</option>
                            <option>USD</option>
                        </select>
                    </div>
                </div>
                
                <!-- Mobile Menu Button -->
                <button class="lg:hidden" id="mobile-menu-btn">
                    <i data-lucide="menu" width="24"></i>
                </button>
            </div>
            
            <!-- Mobile Menu -->
            <div class="lg:hidden hidden" id="mobile-menu">
                <div class="px-2 pt-2 pb-3 space-y-1">
                    <a href="#finance" class="block px-3 py-2 text-gray-700">Финансы</a>
                    <a href="#tech" class="block px-3 py-2 text-gray-700">Технологии</a>
                    <a href="#health" class="block px-3 py-2 text-gray-700">Здоровье</a>
                    <a href="#education" class="block px-3 py-2 text-gray-700">Образование</a>
                    <a href="#travel" class="block px-3 py-2 text-gray-700">Туризм</a>
                    <a href="#business" class="block px-3 py-2 text-gray-700">Бизнес</a>
                    <a href="#blog" class="block px-3 py-2 text-gray-700">Блог</a>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Hero Section -->
    <section class="relative overflow-hidden">
        <div class="absolute inset-0 gradient-bg"></div>
        <div class="absolute inset-0 bg-black opacity-20"></div>
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 animate-slide-up">
                    Найдите лучшие предложения для жизни в СНГ
                </h1>
                <p class="text-xl text-gray-200 mb-8 animate-slide-up" style="animation-delay: 0.2s;">
                    AI-поиск среди тысяч проверенных офферов. Сравнивайте, выбирайте, экономьте.
                </p>
                <button class="bg-accent hover:bg-green-600 text-white font-bold py-4 px-8 rounded-lg text-lg animate-bounce-slow">
                    Начать поиск
                </button>
            </div>
            
            <!-- Floating Elements -->
            <div class="absolute top-20 left-10 opacity-20">
                <i data-lucide="credit-card" width="60" class="text-white"></i>
            </div>
            <div class="absolute bottom-20 right-10 opacity-20">
                <i data-lucide="plane" width="60" class="text-white"></i>
            </div>
        </div>
    </section>
    
    <!-- Categories Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center mb-12">Популярные категории</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Finance -->
                <div class="card-hover bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="credit-card" class="text-primary" width="24"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">💰 Финансы</h3>
                    <p class="text-sm text-gray-600 mb-3">Кредитные карты, инвестиции, страхование</p>
                    <div class="text-sm text-primary">100+ офферов</div>
                </div>
                
                <!-- Tech -->
                <div class="card-hover bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="laptop" class="text-purple-600" width="24"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">🚀 Технологии</h3>
                    <p class="text-sm text-gray-600 mb-3">SaaS, хостинг, безопасность</p>
                    <div class="text-sm text-primary">200+ офферов</div>
                </div>
                
                <!-- Health -->
                <div class="card-hover bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="heart" class="text-red-600" width="24"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">🏥 Здоровье</h3>
                    <p class="text-sm text-gray-600 mb-3">БАДы, фитнес, красота</p>
                    <div class="text-sm text-primary">150+ офферов</div>
                </div>
                
                <!-- Education -->
                <div class="card-hover bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="book" class="text-green-600" width="24"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">📚 Образование</h3>
                    <p class="text-sm text-gray-600 mb-3">Онлайн курсы, сертификаты</p>
                    <div class="text-sm text-primary">80+ офферов</div>
                </div>
                
                <!-- Travel -->
                <div class="card-hover bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="plane" class="text-blue-600" width="24"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">✈️ Туризм</h3>
                    <p class="text-sm text-gray-600 mb-3">Отели, авиабилеты, страхование</p>
                    <div class="text-sm text-primary">120+ офферов</div>
                </div>
                
                <!-- Business -->
                <div class="card-hover bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="briefcase" class="text-indigo-600" width="24"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">📋 Бизнес</h3>
                    <p class="text-sm text-gray-600 mb-3">Бизнес-софт, услуги, консалтинг</p>
                    <div class="text-sm text-primary">90+ офферов</div>
                </div>
                
                <!-- Blog -->
                <div class="card-hover bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="pen-tool" class="text-orange-600" width="24"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">📝 Блог</h3>
                    <p class="text-sm text-gray-600 mb-3">Обзоры, инструкции, новости</p>
                    <div class="text-sm text-primary">50+ статей</div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Popular Offers Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center mb-12">Популярные офферы</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Offer 1 -->
                <div class="card-hover bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="relative">
                        <img src="https://via.placeholder.com/400x200/2563EB/white?text=Тинькофф+Black" alt="Тинькофф Black" class="w-full h-48 object-cover">
                        <span class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">ТОП</span>
                    </div>
                    <div class="p-6">
                        <h3 class="font-bold text-lg mb-2">Тинькофф Black</h3>
                        <p class="text-sm text-gray-600 mb-2">Кредитные карты</p>
                        <div class="flex items-center mb-3">
                            <div class="flex text-yellow-400">
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                            </div>
                            <span class="text-sm text-gray-600 ml-2">4.8/5</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-green-600">до 5,000 ₽</span>
                            <button class="bg-primary hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                Получить
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Offer 2 -->
                <div class="card-hover bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="relative">
                        <img src="https://via.placeholder.com/400x200/10B981/white?text=Яндекс+Облако" alt="Яндекс Облако" class="w-full h-48 object-cover">
                        <span class="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-bold">НОВОЕ</span>
                    </div>
                    <div class="p-6">
                        <h3 class="font-bold text-lg mb-2">Яндекс.Облако</h3>
                        <p class="text-sm text-gray-600 mb-2">Облачные технологии</p>
                        <div class="flex items-center mb-3">
                            <div class="flex text-yellow-400">
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star-half" width="16" fill="currentColor"></i>
                            </div>
                            <span class="text-sm text-gray-600 ml-2">4.6/5</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-green-600">до 3,000 ₽</span>
                            <button class="bg-primary hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                Получить
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Offer 3 -->
                <div class="card-hover bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="relative">
                        <img src="https://via.placeholder.com/400x200/F59E0B/white?text=MyProtein" alt="MyProtein" class="w-full h-48 object-cover">
                        <span class="absolute top-2 left-2 bg-purple-500 text-white px-2 py-1 rounded text-xs font-bold">ХИТ</span>
                    </div>
                    <div class="p-6">
                        <h3 class="font-bold text-lg mb-2">MyProtein</h3>
                        <p class="text-sm text-gray-600 mb-2">Спортивное питание</p>
                        <div class="flex items-center mb-3">
                            <div class="flex text-yellow-400">
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                            </div>
                            <span class="text-sm text-gray-600 ml-2">4.9/5</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-green-600">до 2,500 ₽</span>
                            <button class="bg-primary hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                Получить
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Offer 4 -->
                <div class="card-hover bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="relative">
                        <img src="https://via.placeholder.com/400x200/EF4444/white?text=Skillbox" alt="Skillbox" class="w-full h-48 object-cover">
                        <span class="absolute top-2 left-2 bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold">ОБРАЗОВАНИЕ</span>
                    </div>
                    <div class="p-6">
                        <h3 class="font-bold text-lg mb-2">Skillbox</h3>
                        <p class="text-sm text-gray-600 mb-2">Онлайн курсы</p>
                        <div class="flex items-center mb-3">
                            <div class="flex text-yellow-400">
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star-half" width="16" fill="currentColor"></i>
                            </div>
                            <span class="text-sm text-gray-600 ml-2">4.7/5</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-green-600">до 4,000 ₽</span>
                            <button class="bg-primary hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                Получить
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Offer 5 -->
                <div class="card-hover bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="relative">
                        <img src="https://via.placeholder.com/400x200/8B5CF6/white?text=Booking.com" alt="Booking.com" class="w-full h-48 object-cover">
                        <span class="absolute top-2 left-2 bg-indigo-500 text-white px-2 py-1 rounded text-xs font-bold">ПУТЕШЕСТВИЯ</span>
                    </div>
                    <div class="p-6">
                        <h3 class="font-bold text-lg mb-2">Booking.com</h3>
                        <p class="text-sm text-gray-600 mb-2">Отели и жилье</p>
                        <div class="flex items-center mb-3">
                            <div class="flex text-yellow-400">
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                            </div>
                            <span class="text-sm text-gray-600 ml-2">4.8/5</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-green-600">до 1,500 ₽</span>
                            <button class="bg-primary hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                Получить
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Offer 6 -->
                <div class="card-hover bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="relative">
                        <img src="https://via.placeholder.com/400x200/EC4899/white?text=Битрикс24" alt="Битрикс24" class="w-full h-48 object-cover">
                        <span class="absolute top-2 left-2 bg-pink-500 text-white px-2 py-1 rounded text-xs font-bold">БИЗНЕС</span>
                    </div>
                    <div class="p-6">
                        <h3 class="font-bold text-lg mb-2">Битрикс24</h3>
                        <p class="text-sm text-gray-600 mb-2">CRM система</p>
                        <div class="flex items-center mb-3">
                            <div class="flex text-yellow-400">
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star" width="16" fill="currentColor"></i>
                                <i data-lucide="star-half" width="16" fill="currentColor"></i>
                            </div>
                            <span class="text-sm text-gray-600 ml-2">4.5/5</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-green-600">до 6,000 ₽</span>
                            <button class="bg-primary hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                Получить
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Features Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center mb-12">Почему выбирают нас</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- AI Search -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="brain" class="text-white" width="32"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">AI-поиск</h3>
                    <p class="text-gray-600">Умный поиск находит именно то, что нужно</p>
                </div>
                
                <!-- Verified Offers -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="shield-check" class="text-white" width="32"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Проверенные офферы</h3>
                    <p class="text-gray-600">Только надежные партнеры</p>
                </div>
                
                <!-- Best Conditions -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="trending-up" class="text-white" width="32"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Лучшие условия</h3>
                    <p class="text-gray-600">Эксклюзивные предложения для СНГ</p>
                </div>
                
                <!-- 24/7 Support -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="headphones" class="text-white" width="32"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Поддержка 24/7</h3>
                    <p class="text-gray-600">Помощь на родном языке</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Statistics Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold mb-12">Доверяют тысячи</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div>
                        <div class="text-4xl font-bold text-primary mb-2">50,000+</div>
                        <p class="text-gray-600">довольных пользователей</p>
                    </div>
                    <div>
                        <div class="text-4xl font-bold text-primary mb-2">1,000+</div>
                        <p class="text-gray-600">проверенных офферов</p>
                    </div>
                    <div>
                        <div class="text-4xl font-bold text-primary mb-2">99.9%</div>
                        <p class="text-gray-600">время работы</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Categories -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Категории</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Финансы</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Технологии</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Здоровье</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Образование</a></li>
                    </ul>
                </div>
                
                <!-- Support -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Поддержка</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Помощь</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">FAQ</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Контакты</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Чат</a></li>
                    </ul>
                </div>
                
                <!-- About -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">О компании</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">О нас</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Блог</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Команда</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Карьера</a></li>
                    </ul>
                </div>
                
                <!-- Partners -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Партнёрам</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Стать партнёром</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">API</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Документация</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-gray-400 mb-4 md:mb-0">
                        © 2024 EasyLinkLife.com. Все права защищены.
                    </div>
                    
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white">
                            <i data-lucide="telegram" width="24"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <i data-lucide="youtube" width="24"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <i data-lucide="twitter" width="24"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
        
        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);
        
        document.querySelectorAll('.card-hover').forEach(card => {
            observer.observe(card);
        });
        
        // Auto-detect region
        const userRegion = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const regionMap = {
            'Europe/Moscow': 'RU',
            'Asia/Almaty': 'KZ',
            'Europe/Minsk': 'BY',
            'Asia/Tashkent': 'UZ'
        };
        
        const detectedRegion = regionMap[userRegion] || 'RU';
        console.log(`Detected region: ${detectedRegion}`);
    </script>
</body>
</html>