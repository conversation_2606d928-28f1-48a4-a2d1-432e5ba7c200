# Design Brief - EasyLinkLife.com

## 🎯 Краткое описание проекта

**EasyLinkLife.com** - это современная аффилиатная платформа для стран СНГ с AI-поиском, автоматической генерацией контента и мультирегиональной поддержкой. Платформа объединяет тысячи проверенных офферов в 7 основных категориях.

## 🎨 Дизайн-система

### Брендинг
- **Название**: EasyLinkLife
- **Слоган**: "Лучшие предложения для жизни в СНГ"
- **Позиционирование**: Надежная, современная, удобная платформа
- **Тон коммуникации**: Дружелюбный, профессиональный, доверительный

### Цветовая палитра
```css
/* Основные цвета */
--primary-blue: #2563EB;      /* Основной синий */
--primary-dark: #1D4ED8;      /* Темный синий */
--accent-green: #10B981;      /* Акцентный зеленый */
--accent-orange: #F59E0B;     /* Предупреждения */

/* Нейтральные */
--gray-50: #F9FAFB;
--gray-100: #F3F4F6;
--gray-200: #E5E7EB;
--gray-500: #6B7280;
--gray-900: #111827;

/* Семантические */
--success: #10B981;
--warning: #F59E0B;
--error: #EF4444;
--info: #3B82F6;
```

### Типографика
```css
/* Шрифты */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
--font-secondary: 'Roboto', sans-serif;

/* Размеры */
--text-xs: 12px;
--text-sm: 14px;
--text-base: 16px;
--text-lg: 18px;
--text-xl: 20px;
--text-2xl: 24px;
--text-3xl: 30px;
--text-4xl: 36px;
--text-5xl: 48px;
```

## 📱 Компоненты интерфейса

### Header
- **Высота**: 80px на desktop, 64px на mobile
- **Логотип**: Слева, размер 180x40px
- **Навигация**: Горизонтальное меню с 7 категориями
- **Поиск**: Центральный элемент, ширина 400px
- **Переключатели**: Язык и валюта справа

### Поисковая строка
- **Плейсхолдер**: "Найти кредитные карты, курсы, страховки..."
- **Автокомплит**: Выпадающий список с предложениями
- **Иконка поиска**: Справа в поле
- **Горячие клавиши**: Ctrl+K для фокуса

### Карточки офферов
```css
.offer-card {
  width: 280px;
  height: 320px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.offer-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
```

### Кнопки
```css
/* Основная кнопка */
.btn-primary {
  background: linear-gradient(135deg, #2563EB, #1D4ED8);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
}

/* Вторичная кнопка */
.btn-secondary {
  background: white;
  color: #2563EB;
  border: 2px solid #2563EB;
  padding: 10px 22px;
  border-radius: 8px;
}
```

## 🏗️ Структура страниц

### Главная страница
1. **Header** (80px)
2. **Hero Section** (600px)
3. **Категории** (400px)
4. **Популярные офферы** (500px)
5. **Преимущества** (300px)
6. **Статистика** (200px)
7. **Отзывы** (400px)
8. **FAQ** (300px)
9. **Footer** (200px)

### Страница категории
1. **Header** (80px)
2. **Breadcrumbs** (40px)
3. **Заголовок категории** (120px)
4. **Фильтры** (боковая панель 300px)
5. **Список офферов** (основная область)
6. **Пагинация** (60px)
7. **Footer** (200px)

### Страница оффера
1. **Header** (80px)
2. **Breadcrumbs** (40px)
3. **Основная информация** (400px)
4. **Описание и условия** (300px)
5. **FAQ** (200px)
6. **Похожие офферы** (300px)
7. **Footer** (200px)

## 🎨 Визуальные элементы

### Иконки
- **Стиль**: Outline, 24px по умолчанию
- **Библиотека**: Heroicons или Lucide
- **Цвет**: --gray-500 по умолчанию, --primary-blue для активных

### Изображения
- **Логотипы офферов**: 80x80px, формат PNG/SVG
- **Категории**: 120x120px иллюстрации
- **Hero изображения**: 1200x600px, WebP формат
- **Аватары**: 40x40px, круглые

### Анимации
```css
/* Плавные переходы */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Появление элементов */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## 📊 Сетка и отступы

### Grid система
```css
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Колонки */
.grid-2 { display: grid; grid-template-columns: repeat(2, 1fr); gap: 24px; }
.grid-3 { display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px; }
.grid-4 { display: grid; grid-template-columns: repeat(4, 1fr); gap: 24px; }
```

### Отступы
```css
/* Spacing scale */
--space-1: 4px;
--space-2: 8px;
--space-3: 12px;
--space-4: 16px;
--space-5: 20px;
--space-6: 24px;
--space-8: 32px;
--space-10: 40px;
--space-12: 48px;
--space-16: 64px;
--space-20: 80px;
```

## 🌍 Локализация

### Языковые версии
- **Русский** (основной): ru.easylinklife.com
- **Казахский**: kz.easylinklife.com  
- **Белорусский**: by.easylinklife.com
- **Узбекский**: uz.easylinklife.com

### Валютные символы
```css
.currency-rub::before { content: "₽"; }
.currency-kzt::before { content: "₸"; }
.currency-byn::before { content: "Br"; }
.currency-usd::before { content: "$"; }
```

### Флаги стран
- **Размер**: 24x16px
- **Формат**: SVG
- **Стиль**: Прямоугольные с закругленными углами

## 📱 Адаптивность

### Breakpoints
```css
/* Mobile first */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
```

### Мобильная версия
- **Гамбургер меню**: 3 линии, анимация в X
- **Поиск**: Полная ширина, 48px высота
- **Карточки**: 1 колонка, отступы 16px
- **Touch targets**: Минимум 44px

## 🎯 UX принципы

### Навигация
- **Хлебные крошки** на всех страницах кроме главной
- **Активные состояния** для текущей страницы
- **Hover эффекты** для интерактивных элементов
- **Loading states** для асинхронных операций

### Формы
- **Валидация** в реальном времени
- **Четкие лейблы** и плейсхолдеры
- **Сообщения об ошибках** под полями
- **Прогресс индикаторы** для многошаговых форм

### Обратная связь
- **Toast уведомления** для действий пользователя
- **Skeleton loaders** во время загрузки
- **Empty states** для пустых списков
- **Error boundaries** для обработки ошибок

## 🔍 Accessibility

### WCAG 2.1 AA соответствие
- **Контрастность**: минимум 4.5:1 для обычного текста
- **Keyboard navigation**: все элементы доступны с клавиатуры
- **Screen readers**: семантическая разметка
- **Focus indicators**: видимые индикаторы фокуса

### Семантическая разметка
```html
<header role="banner">
<nav role="navigation">
<main role="main">
<aside role="complementary">
<footer role="contentinfo">
```

## 📊 Метрики дизайна

### Performance
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **Time to Interactive**: < 3.5s

### Usability
- **Task completion rate**: > 90%
- **Error rate**: < 5%
- **User satisfaction**: > 4.5/5
- **Mobile usability**: 100% Google score

Этот design brief служит основой для создания консистентного и пользователь-ориентированного интерфейса EasyLinkLife.com.