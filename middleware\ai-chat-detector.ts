export class AIChatDetectorMiddleware {
  private aiUserAgents = [
    'ChatGPT-User',
    'GPTBot',
    'Claude-Web',
    'Bard',
    'PerplexityBot',
    'You.com-Bot'
  ];
  
  use(req: Request, res: Response, next: NextFunction) {
    const userAgent = req.headers['user-agent'] || '';
    const isAIChat = this.detectAIChat(userAgent, req);
    
    if (isAIChat) {
      // Добавляем специальные заголовки для AI
      res.setHeader('X-AI-Friendly', 'true');
      res.setHeader('X-Content-Type', 'structured');
      
      // Логируем AI запрос
      console.log(`AI Chat detected: ${userAgent}`, {
        ip: req.ip,
        path: req.path,
        query: req.query,
        timestamp: new Date().toISOString()
      });
      
      // Добавляем контекст для AI в request
      req.aiContext = {
        isAIChat: true,
        chatType: this.identifyChatType(userAgent),
        preferredFormat: 'structured',
        maxResponseLength: 2000
      };
    }
    
    next();
  }
  
  private detectAIChat(userAgent: string, req: Request): boolean {
    // Проверяем User-Agent
    if (this.aiUserAgents.some(agent => userAgent.includes(agent))) {
      return true;
    }
    
    // Проверяем специальные заголовки
    if (req.headers['x-ai-request'] === 'true') {
      return true;
    }
    
    // Проверяем паттерны запросов
    const aiPatterns = [
      /api\/ai\//,
      /\?format=ai/,
      /\?ai=true/
    ];
    
    return aiPatterns.some(pattern => pattern.test(req.url));
  }
  
  private identifyChatType(userAgent: string): string {
    if (userAgent.includes('ChatGPT')) return 'chatgpt';
    if (userAgent.includes('Claude')) return 'claude';
    if (userAgent.includes('Bard')) return 'bard';
    if (userAgent.includes('Perplexity')) return 'perplexity';
    return 'unknown';
  }
}