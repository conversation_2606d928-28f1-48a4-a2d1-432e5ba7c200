# Дизайн системы EasyLinkLife.com

## Обзор

EasyLinkLife.com представляет собой высоконагруженную аффилиатную платформу, развернутую на сервере 185.207.65.27, построенную на микросервисной архитектуре с использованием современных технологий. Система обеспечивает обработку более 1000 RPS с временем отклика менее 300ms и поддерживает 99.9% времени работы.

Платформа интегрирует передовые технологии AI (RAG-поиск через OpenAI), автоматическую генерацию SEO-контента, мультирегиональную поддержку для 11 стран СНГ и комплексную систему защиты от мошенничества.

## Архитектура

### Общая архитектура системы

```mermaid
graph TB
    subgraph "🌐 Internet Layer"
        U[Users] 
        B[Search Bots]
    end
    
    subgraph "☁️ Cloudflare Edge"
        CF[CDN + WAF]
        DDOS[DDoS Protection]
    end
    
    subgraph "🖥️ Production Server"
        subgraph "🛡️ Security Layer"
            NGINX[Nginx + ModSecurity]
            UFW[UFW Firewall]
            F2B[Fail2ban]
        end
        
        subgraph "🚪 API Gateway"
            KONG[Kong Gateway]
            AUTH[JWT Auth]
            RATE[Rate Limiting]
        end
        
        subgraph "⚙️ Application Services"
            WEB[Next.js 14 SSR/ISR]
            OFF[Offer Service]
            SEARCH[Search Service]
            EMAIL[Email Service]
            GEO[Geo Service]
            FRAUD[Fraud Detection]
        end
        
        subgraph "🔄 Background Workers"
            IMP[Offer Importer]
            SEO[SEO Generator]
            CURR[Currency Updater]
            CHECK[Offer Checker]
        end
        
        subgraph "💾 Data Layer"
            PG[(PostgreSQL 16)]
            PGB[PgBouncer]
            REDIS[(Redis Sentinel)]
            MEILI[(Meilisearch)]
            NEO4J[(Neo4j)]
            MINIO[MinIO S3]
        end
        
        subgraph "📊 Monitoring"
            PROM[Prometheus]
            GRAF[Grafana]
            LOKI[Loki]
            ALERT[Alertmanager]
        end
    end
    
    U --> CF
    B --> CF
    CF --> NGINX
    NGINX --> KONG
    KONG --> WEB
    KONG --> OFF
    KONG --> SEARCH
    
    WEB --> PGB
    OFF --> PGB
    SEARCH --> MEILI
    
    PGB --> PG
    OFF --> REDIS
    SEARCH --> NEO4J
```

### Технологический стек

#### Frontend
- **Framework**: Next.js 14 с App Router
- **Language**: TypeScript (строгий режим)
- **Styling**: Tailwind CSS с custom design system
- **State Management**: Zustand для глобального состояния
- **Forms**: React Hook Form с Zod валидацией
- **HTTP Client**: Axios с interceptors
- **SEO**: Next.js встроенные возможности + Schema.org

#### Backend Services
- **Runtime**: Node.js 20 LTS
- **Framework**: Fastify для высокой производительности
- **Language**: TypeScript с строгой типизацией
- **API Gateway**: Kong с декларативной конфигурацией
- **Authentication**: JWT с refresh tokens
- **Validation**: Joi для входящих данных

#### Базы данных
- **Primary DB**: PostgreSQL 16 с партиционированием
- **Connection Pool**: PgBouncer (transaction mode)
- **Cache**: Redis 7 с Sentinel для HA
- **Search**: Meilisearch с OpenAI векторизацией
- **Graph DB**: Neo4j для связей между офферами
- **Object Storage**: MinIO S3-compatible

#### Infrastructure
- **Containerization**: Docker с multi-stage builds
- **Orchestration**: Docker Compose
- **Reverse Proxy**: Nginx с ModSecurity WAF
- **CDN**: Cloudflare с edge caching
- **Monitoring**: Prometheus + Grafana + Loki stack

## Компоненты и интерфейсы

### 1. Web Application (Next.js)

#### Структура приложения
```
apps/web/
├── app/                    # App Router pages
│   ├── (auth)/            # Auth group
│   ├── [locale]/          # Internationalization
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── components/            # Reusable components
│   ├── ui/               # Base UI components
│   ├── forms/            # Form components
│   └── layout/           # Layout components
├── lib/                  # Utilities
│   ├── api.ts           # API client
│   ├── auth.ts          # Auth helpers
│   └── utils.ts         # Common utilities
└── types/               # TypeScript definitions
```

#### Ключевые компоненты
- **SearchBar**: AI-powered поиск с автокомплитом
- **OfferCard**: Карточка оффера с рейтингом и CTA
- **CategoryGrid**: Сетка категорий с фильтрацией
- **CurrencyConverter**: Конвертер валют в реальном времени
- **FraudProtection**: Клиентская защита от ботов

### 2. Offer Service

#### API Endpoints
```typescript
interface OfferService {
  // Получение офферов
  GET /api/v1/offers
  GET /api/v1/offers/:id
  GET /api/v1/offers/category/:slug
  
  // Клики и трекинг
  POST /api/v1/offers/:id/click
  GET /api/v1/offers/:id/stats
  
  // Управление (admin)
  POST /api/v1/offers
  PUT /api/v1/offers/:id
  DELETE /api/v1/offers/:id
}
```

#### Модель данных
```typescript
interface Offer {
  id: string;
  external_id: string;
  network_id: string;
  
  // Локализованный контент
  title: Record<string, string>;
  description: Record<string, string>;
  short_description: string;
  
  // URLs
  url: string;
  tracking_url: string;
  canonical_url: string;
  
  // Media
  images: string[];
  videos: string[];
  
  // Финансовые данные
  payouts: Payout[];
  payouts_multi: Record<string, Payout[]>;
  epc: number;
  cr: number;
  
  // Таргетинг
  geo: string[];
  devices: string[];
  traffic_sources: string[];
  
  // SEO
  seo: SEOData;
  faq: FAQItem[];
  pros: string[];
  cons: string[];
  
  // Статистика
  impressions: number;
  clicks: number;
  conversions: number;
  
  // Статус
  status: 'active' | 'inactive' | 'pending';
  is_top: boolean;
  is_featured: boolean;
  
  // Временные метки
  created_at: Date;
  updated_at: Date;
  expires_at?: Date;
}
```

### 3. Search Service (RAG-powered)

#### Архитектура поиска
```mermaid
graph LR
    A[User Query] --> B[Query Processor]
    B --> C[OpenAI Embeddings]
    B --> D[Meilisearch Index]
    C --> E[Vector Search]
    D --> F[Text Search]
    E --> G[Result Merger]
    F --> G
    G --> H[Ranking Algorithm]
    H --> I[Filtered Results]
```

#### API интерфейс
```typescript
interface SearchService {
  // Основной поиск
  POST /api/v1/search
  
  // Автокомплит
  GET /api/v1/search/suggest
  
  // Фасетная фильтрация
  GET /api/v1/search/facets
  
  // Похожие офферы
  GET /api/v1/search/similar/:offerId
}

interface SearchRequest {
  query: string;
  filters: {
    categories?: string[];
    geo?: string[];
    payouts?: { min?: number; max?: number };
    currency?: string;
  };
  sort: 'relevance' | 'payout' | 'cr' | 'updated';
  page: number;
  limit: number;
}
```

### 4. Fraud Detection Service

#### Система скоринга
```typescript
interface FraudDetectionService {
  async checkClick(data: ClickData): Promise<FraudResult> {
    const checks = await Promise.all([
      this.checkIPReputation(data.ip),
      this.checkClickFrequency(data.ip, data.offerId),
      this.checkUserAgent(data.userAgent),
      this.checkTimePattern(data.ip, data.timestamp),
      this.checkReferrer(data.referrer)
    ]);
    
    const totalScore = checks.reduce((sum, check) => sum + check.score, 0);
    const blocked = totalScore >= 80;
    
    return {
      score: totalScore,
      blocked,
      reasons: checks.flatMap(c => c.reasons)
    };
  }
}
```

### 5. Currency Updater Worker

#### Мультипровайдерная система
```typescript
class CurrencyUpdater {
  private providers = [
    new FixerProvider(),
    new ExchangeRateProvider(),
    new CurrencyLayerProvider()
  ];
  
  async updateRates(): Promise<void> {
    const baseCurrencies = ['USD', 'EUR'];
    const targetCurrencies = ['RUB', 'KZT', 'BYN', 'UZS', 'KGS', 'TJS', 'TMT', 'AMD', 'AZN', 'GEL', 'MDL'];
    
    for (const base of baseCurrencies) {
      const rates = await this.fetchWithFallback(base, targetCurrencies);
      await this.saveRates(base, rates);
    }
  }
  
  private async fetchWithFallback(base: string, targets: string[]): Promise<ExchangeRates> {
    for (const provider of this.providers) {
      try {
        return await provider.getRates(base, targets);
      } catch (error) {
        logger.warn(`Provider ${provider.name} failed:`, error);
      }
    }
    throw new Error('All currency providers failed');
  }
}
```

## Модели данных

### Схема базы данных PostgreSQL

```sql
-- Партиционированная таблица офферов
CREATE TABLE offers (
    id UUID DEFAULT uuid_generate_v4(),
    external_id VARCHAR(255) NOT NULL,
    network_id VARCHAR(50) NOT NULL,
    
    -- Локализованный контент
    title JSONB NOT NULL,
    description JSONB NOT NULL,
    short_description TEXT,
    
    -- URLs
    url TEXT NOT NULL,
    tracking_url TEXT NOT NULL,
    canonical_url TEXT,
    
    -- Media
    images JSONB DEFAULT '[]',
    videos JSONB DEFAULT '[]',
    
    -- Финансовые данные (мультивалютность)
    payouts JSONB NOT NULL,
    payouts_multi JSONB DEFAULT '{}',
    epc DECIMAL(10, 2) DEFAULT 0,
    cr DECIMAL(5, 2) DEFAULT 0,
    
    -- Таргетинг
    geo TEXT[] DEFAULT '{}',
    devices TEXT[] DEFAULT '{}',
    traffic_sources TEXT[] DEFAULT '{}',
    
    -- SEO
    seo JSONB DEFAULT '{}',
    faq JSONB DEFAULT '[]',
    pros TEXT[] DEFAULT '{}',
    cons TEXT[] DEFAULT '{}',
    
    -- Статистика
    impressions BIGINT DEFAULT 0,
    clicks BIGINT DEFAULT 0,
    conversions BIGINT DEFAULT 0,
    
    -- Статус
    status VARCHAR(20) DEFAULT 'active',
    is_top BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    
    -- Временные метки
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_checked_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    
    PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (created_at);

-- Курсы валют
CREATE TABLE exchange_rates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    base_currency VARCHAR(3) NOT NULL,
    target_currency VARCHAR(3) NOT NULL,
    rate DECIMAL(20, 10) NOT NULL,
    source VARCHAR(50) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(base_currency, target_currency, created_at)
);

-- Региональные настройки
CREATE TABLE offer_regions (
    offer_id UUID NOT NULL,
    country_code VARCHAR(2) NOT NULL,
    city VARCHAR(100),
    is_available BOOLEAN DEFAULT TRUE,
    custom_title JSONB,
    custom_description JSONB,
    min_amount DECIMAL(20, 2),
    max_amount DECIMAL(20, 2),
    currency VARCHAR(3),
    PRIMARY KEY (offer_id, country_code, COALESCE(city, ''))
);

-- Логи мошенничества
CREATE TABLE fraud_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ip INET NOT NULL,
    user_agent TEXT,
    offer_id UUID,
    score INTEGER NOT NULL,
    reasons JSONB,
    blocked BOOLEAN DEFAULT FALSE,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Индексы для производительности
CREATE INDEX CONCURRENTLY idx_offers_external_id ON offers(external_id);
CREATE INDEX CONCURRENTLY idx_offers_status ON offers(status) WHERE status = 'active';
CREATE INDEX CONCURRENTLY idx_offers_geo_gin ON offers USING GIN(geo);
CREATE INDEX CONCURRENTLY idx_offers_payouts_multi ON offers USING GIN(payouts_multi);
CREATE INDEX CONCURRENTLY idx_exchange_rates_lookup ON exchange_rates(base_currency, target_currency, created_at DESC);
CREATE INDEX CONCURRENTLY idx_fraud_logs_ip_time ON fraud_logs(ip, timestamp);
```

### Redis структуры данных

```typescript
// Кеширование офферов
interface OfferCache {
  key: `offer:${string}`;
  value: Offer;
  ttl: 3600; // 1 час
}

// Сессии пользователей
interface UserSession {
  key: `session:${string}`;
  value: {
    userId?: string;
    country: string;
    language: string;
    currency: string;
    preferences: UserPreferences;
  };
  ttl: 86400; // 24 часа
}

// Fraud detection данные
interface FraudData {
  key: `fraud:${string}:${string}`; // ip:offerId
  value: number; // количество кликов
  ttl: 3600; // 1 час
}

// Курсы валют
interface CurrencyCache {
  key: `rates:${string}:${string}`; // base:target
  value: {
    rate: number;
    source: string;
    timestamp: number;
  };
  ttl: 1800; // 30 минут
}
```

## Обработка ошибок

### Стратегия обработки ошибок

```typescript
// Централизованная обработка ошибок
class ErrorHandler {
  static handle(error: Error, context: string): ErrorResponse {
    // Логирование
    logger.error(`Error in ${context}:`, {
      message: error.message,
      stack: error.stack,
      context
    });
    
    // Классификация ошибок
    if (error instanceof ValidationError) {
      return {
        status: 400,
        code: 'VALIDATION_ERROR',
        message: error.message,
        details: error.details
      };
    }
    
    if (error instanceof NotFoundError) {
      return {
        status: 404,
        code: 'NOT_FOUND',
        message: 'Resource not found'
      };
    }
    
    if (error instanceof DatabaseError) {
      return {
        status: 500,
        code: 'DATABASE_ERROR',
        message: 'Internal server error'
      };
    }
    
    // Неизвестная ошибка
    return {
      status: 500,
      code: 'INTERNAL_ERROR',
      message: 'Internal server error'
    };
  }
}

// Circuit Breaker для внешних сервисов
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor(
    private threshold = 5,
    private timeout = 60000,
    private resetTimeout = 30000
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.resetTimeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }
    
    try {
      const result = await Promise.race([
        operation(),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), this.timeout)
        )
      ]);
      
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }
  
  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.threshold) {
      this.state = 'OPEN';
    }
  }
}
```

### Graceful Degradation

```typescript
// Стратегия деградации для поиска
class SearchService {
  async search(query: SearchRequest): Promise<SearchResponse> {
    try {
      // Попытка RAG-поиска с AI
      return await this.ragSearch(query);
    } catch (error) {
      logger.warn('RAG search failed, falling back to basic search:', error);
      
      try {
        // Fallback на обычный поиск Meilisearch
        return await this.basicSearch(query);
      } catch (error) {
        logger.error('All search methods failed, returning cached results:', error);
        
        // Последний fallback - кешированные результаты
        return await this.getCachedResults(query);
      }
    }
  }
}

// Деградация для валютной конвертации
class CurrencyService {
  async convert(amount: number, from: string, to: string): Promise<number> {
    try {
      // Попытка получить актуальный курс
      const rate = await this.getCurrentRate(from, to);
      return amount * rate;
    } catch (error) {
      logger.warn('Current rate fetch failed, using cached rate:', error);
      
      try {
        // Fallback на кешированный курс
        const cachedRate = await this.getCachedRate(from, to);
        return amount * cachedRate;
      } catch (error) {
        logger.error('All currency conversion failed, returning original amount:', error);
        
        // Последний fallback - возврат оригинальной суммы
        return amount;
      }
    }
  }
}
```

## Стратегия тестирования

### Пирамида тестирования

```mermaid
graph TD
    A[E2E Tests] --> B[Integration Tests]
    B --> C[Unit Tests]
    
    A1[Playwright<br/>Critical User Journeys] --> A
    B1[API Tests<br/>Service Integration<br/>Database Tests] --> B
    C1[Jest<br/>Component Tests<br/>Business Logic] --> C
```

### Типы тестов

#### Unit Tests (Jest)
```typescript
// Пример unit теста для fraud detection
describe('FraudDetectionService', () => {
  let service: FraudDetectionService;
  
  beforeEach(() => {
    service = new FraudDetectionService(mockRedis, mockDb);
  });
  
  describe('checkClick', () => {
    it('should block click with high fraud score', async () => {
      const clickData = {
        ip: '***********',
        userAgent: 'bot',
        offerId: 'test-offer',
        timestamp: Date.now()
      };
      
      const result = await service.checkClick(clickData);
      
      expect(result.blocked).toBe(true);
      expect(result.score).toBeGreaterThanOrEqual(80);
      expect(result.reasons).toContain('Bot user agent detected');
    });
  });
});
```

#### Integration Tests
```typescript
// Пример интеграционного теста для API
describe('Offer API', () => {
  let app: FastifyInstance;
  
  beforeAll(async () => {
    app = await createTestApp();
  });
  
  describe('GET /api/v1/offers', () => {
    it('should return paginated offers', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/offers?page=1&limit=10'
      });
      
      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.payload);
      expect(data.offers).toHaveLength(10);
      expect(data.pagination.page).toBe(1);
      expect(data.pagination.total).toBeGreaterThan(0);
    });
  });
});
```

#### E2E Tests (Playwright)
```typescript
// Пример E2E теста
test('user can search and click on offer', async ({ page }) => {
  await page.goto('/');
  
  // Поиск оффера
  await page.fill('[data-testid="search-input"]', 'кредитная карта');
  await page.click('[data-testid="search-button"]');
  
  // Проверка результатов
  await expect(page.locator('[data-testid="offer-card"]').first()).toBeVisible();
  
  // Клик на оффер
  await page.click('[data-testid="offer-card"]').first();
  
  // Проверка перехода
  await expect(page).toHaveURL(/\/offers\/[a-z0-9-]+/);
  
  // Клик на CTA
  await page.click('[data-testid="offer-cta"]');
  
  // Проверка fraud detection (не должно блокировать обычного пользователя)
  await expect(page).toHaveURL(/^https:\/\/partner\.com/);
});
```

### Load Testing (K6)
```javascript
// Нагрузочное тестирование поиска
import http from 'k6/http';
import { check, sleep } from 'k6';

export const options = {
  stages: [
    { duration: '2m', target: 100 },
    { duration: '5m', target: 100 },
    { duration: '2m', target: 200 },
    { duration: '5m', target: 200 },
    { duration: '2m', target: 0 },
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'],
    http_req_failed: ['rate<0.05'],
  },
};

export default function () {
  const searchQueries = [
    'кредитная карта',
    'страхование',
    'онлайн курсы',
    'хостинг'
  ];
  
  const query = searchQueries[Math.floor(Math.random() * searchQueries.length)];
  
  const response = http.post('https://easylinklife.com/api/v1/search', {
    query,
    page: 1,
    limit: 20
  });
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
    'has results': (r) => JSON.parse(r.body).hits.length > 0,
  });
  
  sleep(Math.random() * 3 + 1);
}
```

Этот дизайн документ обеспечивает полное техническое описание системы EasyLinkLife.com, включая архитектуру, компоненты, модели данных, обработку ошибок и стратегию тестирования. Все решения основаны на требованиях высокой производительности, масштабируемости и надежности для production-ready системы.